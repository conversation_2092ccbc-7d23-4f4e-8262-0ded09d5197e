<?php

namespace App\Http\API\Dashboard\Google\Controllers;

use App\Domain\Google\Actions\Script\RenderScript;
use App\Domain\Google\Dto\Drive\CreateDriveDto;
use App\Domain\Google\Jobs\Drive\CreateDriveForScriptJob;
use App\Domain\Google\Jobs\Drive\CreateFileForScriptJob;
use App\Domain\Google\Models\GoogleScript;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptStatus;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptType;
use App\Domain\Google\Support\Enums\Drive\GoogleFileType;
use App\Http\API\Dashboard\Google\Requests\GoogleScriptStoreRequest;
use App\Http\API\Dashboard\Google\Resources\GoogleScriptResource;
use App\Http\Support\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Bus;

class GoogleScriptController extends Controller
{
    public function show(int $id): GoogleScriptResource
    {
        $script = GoogleScript::query()->findOrFail($id);

        return GoogleScriptResource::make($script);
    }

    public function store(GoogleScriptStoreRequest $request): GoogleScriptResource
    {
        $drive = $request->getDrive();

        $script = GoogleScript::query()->create([
            GoogleScript::PROPERTY_GOOGLE_AD_ACCOUNT_ID => $request->getAdAccount()->getId(),
            GoogleScript::PROPERTY_GOOGLE_DRIVE_ID => $drive?->getId(),
            GoogleScript::PROPERTY_STATUS => $drive
                ? GoogleScriptStatus::CREATING_FILE
                : GoogleScriptStatus::CREATING_DRIVE,
            GoogleScript::PROPERTY_TYPE => $request->getType(),
        ]);

        if ($request->getType() === GoogleScriptType::QUALITY_SCORE_CHECKER) {
            $jobs = [new CreateFileForScriptJob($script, GoogleFileType::SHEETS)];

            if (!$drive) {
                $dto = (new CreateDriveDto())->setName($request->getDriveName());
                array_unshift($jobs, new CreateDriveForScriptJob($script, $dto));
            }

            Bus::dispatchChain($jobs);
        }

        return GoogleScriptResource::make($script);
    }

    public function script(int $id): JsonResponse
    {
        $script = GoogleScript::query()->findOrFail($id);

        $script = app(RenderScript::class)->execute($script);

        return response()->json($script);
    }
}
