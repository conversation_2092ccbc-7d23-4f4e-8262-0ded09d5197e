<?php

namespace App\Http\API\Dashboard\Google\Requests;

use App\Domain\Google\Models\GoogleAdAccount;
use App\Domain\Google\Models\GoogleDrive;
use App\Domain\Google\Support\Enums\Ads\GoogleScriptType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GoogleScriptStoreRequest extends FormRequest
{
    public const REQUEST_DRIVE_ID = 'drive_id';
    public const REQUEST_DRIVE_NAME = 'drive_name';
    public const REQUEST_AD_ACCOUNT_ID = 'ad_account_id';
    public const REQUEST_TYPE = 'type';

    public function rules(): array
    {
        return [
            self::REQUEST_DRIVE_ID => [
                Rule::requiredIf(!$this->input(self::REQUEST_DRIVE_NAME)),
                'nullable',
                Rule::exists(GoogleDrive::TABLE_NAME, GoogleDrive::PROPERTY_ID),
            ],
            self::REQUEST_DRIVE_NAME => [Rule::requiredIf(!$this->input(self::REQUEST_DRIVE_ID)), 'nullable', 'string'],
            self::REQUEST_AD_ACCOUNT_ID => [
                'required',
                Rule::exists(GoogleAdAccount::TABLE_NAME, GoogleAdAccount::PROPERTY_ID),
            ],
            self::REQUEST_TYPE => ['required', Rule::enum(GoogleScriptType::class)],
        ];
    }

    public function getDrive(): ?GoogleDrive
    {
        return GoogleDrive::query()->find($this->input(self::REQUEST_DRIVE_ID));
    }

    public function getDriveName(): ?string
    {
        return $this->input(self::REQUEST_DRIVE_NAME);
    }

    public function getAdAccount(): GoogleAdAccount
    {
        return GoogleAdAccount::query()->findOrFail($this->input(self::REQUEST_AD_ACCOUNT_ID));
    }

    public function getType(): GoogleScriptType
    {
        return GoogleScriptType::from($this->input(self::REQUEST_TYPE));
    }
}
