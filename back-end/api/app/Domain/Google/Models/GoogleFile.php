<?php

namespace App\Domain\Google\Models;

use App\Domain\Google\Support\Enums\Drive\GoogleFileType;
use App\Domain\Google\Support\Scopes\GoogleFileGlobalAccessScope;
use App\Support\Models\Traits\TimestampableMethods;
use App\Support\Traits\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

class GoogleFile extends Model
{
    use TimestampableMethods;
    use HasFactory;

    public const TABLE_NAME = 'google_files';

    public const PROPERTY_ID = 'id';
    public const PROPERTY_GOOGLE_ACCOUNT_ID = 'google_account_id';
    public const PROPERTY_GOOGLE_DRIVE_ID = 'google_drive_id';
    public const PROPERTY_EXTERNAL_ID = 'external_id';
    public const PROPERTY_NAME = 'name';
    public const PROPERTY_TYPE = 'type';
    public const PROPERTY_URL = 'url';
    public const PROPERTY_CAPABILITIES = 'capabilities';
    public const PROPERTY_EXTERNAL_CREATED_AT = 'external_created_at';
    public const PROPERTY_EXTERNAL_UPDATED_AT = 'external_updated_at';

    public const RELATION_GOOGLE_ACCOUNT = 'googleAccount';
    public const RELATION_GOOGLE_DRIVE = 'googleDrive';

    protected $table = self::TABLE_NAME;

    protected $casts = [
        self::PROPERTY_CAPABILITIES => 'array',
        self::PROPERTY_EXTERNAL_CREATED_AT => 'datetime',
        self::PROPERTY_EXTERNAL_UPDATED_AT => 'datetime',
        self::PROPERTY_TYPE => GoogleFileType::class,
    ];

    protected $guarded = [self::PROPERTY_ID];

    public static function booted(): void
    {
        static::addGlobalScope(new GoogleFileGlobalAccessScope());
    }

    public function googleAccount(): BelongsTo
    {
        return $this->belongsTo(GoogleAccount::class, self::PROPERTY_GOOGLE_ACCOUNT_ID);
    }

    public function getGoogleAccount(): GoogleAccount
    {
        return $this->{self::RELATION_GOOGLE_ACCOUNT};
    }

    public function googleDrive(): BelongsTo
    {
        return $this->belongsTo(GoogleDrive::class, self::PROPERTY_GOOGLE_DRIVE_ID);
    }

    public function getGoogleDrive(): ?GoogleDrive
    {
        return $this->{self::RELATION_GOOGLE_DRIVE};
    }

    public function getId(): int
    {
        return $this->{self::PROPERTY_ID};
    }

    public function setId(int $value): self
    {
        $this->{self::PROPERTY_ID} = $value;
        return $this;
    }

    public function getGoogleAccountId(): int
    {
        return $this->{self::PROPERTY_GOOGLE_ACCOUNT_ID};
    }

    public function setGoogleAccountId(int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_ACCOUNT_ID} = $value;
        return $this;
    }

    public function getExternalId(): string
    {
        return $this->{self::PROPERTY_EXTERNAL_ID};
    }

    public function setExternalId(string $value): self
    {
        $this->{self::PROPERTY_EXTERNAL_ID} = $value;
        return $this;
    }

    public function getName(): string
    {
        return $this->{self::PROPERTY_NAME};
    }

    public function setName(string $value): self
    {
        $this->{self::PROPERTY_NAME} = $value;
        return $this;
    }

    public function getType(): GoogleFileType
    {
        return $this->{self::PROPERTY_TYPE};
    }

    public function setType(GoogleFileType $value): self
    {
        $this->{self::PROPERTY_TYPE} = $value;
        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->{self::PROPERTY_URL};
    }

    public function setUrl(?string $value): self
    {
        $this->{self::PROPERTY_URL} = $value;
        return $this;
    }

    public function getCapabilities(): ?array
    {
        return $this->{self::PROPERTY_CAPABILITIES};
    }

    public function setCapabilities(?array $value): self
    {
        $this->{self::PROPERTY_CAPABILITIES} = $value;
        return $this;
    }

    public function getExternalCreatedAt(): ?Carbon
    {
        return $this->{self::PROPERTY_EXTERNAL_CREATED_AT};
    }

    public function setExternalCreatedAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_EXTERNAL_CREATED_AT} = $value;
        return $this;
    }

    public function getExternalUpdatedAt(): ?Carbon
    {
        return $this->{self::PROPERTY_EXTERNAL_UPDATED_AT};
    }

    public function setExternalUpdatedAt(?Carbon $value): self
    {
        $this->{self::PROPERTY_EXTERNAL_UPDATED_AT} = $value;
        return $this;
    }

    public function getGoogleDriveId(): ?int
    {
        return $this->{self::PROPERTY_GOOGLE_DRIVE_ID};
    }

    public function setGoogleDriveId(?int $value): self
    {
        $this->{self::PROPERTY_GOOGLE_DRIVE_ID} = $value;

        return $this;
    }
}
