<?php

namespace App\Domain\Google\Dto\Sheet;

class SheetTabDto
{
    private int $id;
    private string $name;
    private int $index;
    private int $rowCount;
    private int $columnCount;
    private array $rows = [];

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): SheetTabDto
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): SheetTabDto
    {
        $this->name = $name;
        return $this;
    }

    public function getIndex(): int
    {
        return $this->index;
    }

    public function setIndex(int $index): SheetTabDto
    {
        $this->index = $index;
        return $this;
    }

    public function getRowCount(): int
    {
        return $this->rowCount;
    }

    public function setRowCount(int $rowCount): SheetTabDto
    {
        $this->rowCount = $rowCount;
        return $this;
    }

    public function getColumnCount(): int
    {
        return $this->columnCount;
    }

    public function setColumnCount(int $columnCount): SheetTabDto
    {
        $this->columnCount = $columnCount;
        return $this;
    }

    public function getRows(): array
    {
        return $this->rows;
    }

    public function setRows(array $rows): SheetTabDto
    {
        $this->rows = $rows;
        return $this;
    }
}
