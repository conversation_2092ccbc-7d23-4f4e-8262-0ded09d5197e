<?php

namespace App\Domain\Google\Actions\Sheet;

use App\Domain\Google\Dto\Sheet\SheetDto;
use App\Domain\Google\Dto\Sheet\SheetTabDto;
use App\Domain\Google\Models\GoogleFile;
use App\Domain\Google\Support\Exceptions\GoogleRequestException;
use App\Domain\Google\Support\Requests\GoogleRequest;
use App\Support\Enums\RequestType;
use Illuminate\Support\Arr;

class GetSheetTabs extends GoogleRequest
{
    public const ENDPOINT = 'https://sheets.googleapis.com/v4/spreadsheets/%s';

    public const RESPONSE_SHEETS = 'sheets';
    public const RESPONSE_PROPERTIES = 'properties';
    public const RESPONSE_SHEET_ID = 'sheetId';
    public const RESPONSE_TITLE = 'title';
    public const RESPONSE_INDEX = 'index';
    public const RESPONSE_GRID_PROPERTIES = 'gridProperties';
    public const RESPONSE_ROW_COUNT = 'rowCount';
    public const RESPONSE_COLUMN_COUNT = 'columnCount';

    private GoogleFile $file;

    public function execute(GoogleFile $file): SheetDto
    {
        $this->file = $file;

        $response = $this->getResponse(RequestType::GET, $file->getGoogleAccount());

        if (!$response->isSuccessful()) {
            throw GoogleRequestException::becauseOfHttpErrorWithStatusCode(
                $file->getGoogleAccount(),
                $response->getStatus(),
                $response->getBody(),
            );
        }

        $tabs = $this->processTabs(Arr::get($response->getBody(), self::RESPONSE_SHEETS, []));

        return (new SheetDto())->setTabs($tabs);
    }

    protected function getUrl(): string
    {
        return sprintf(self::ENDPOINT, $this->file->getExternalId());
    }

    protected function getQueryParameters(): ?array
    {
        return [
            'fields' => 'sheets.properties',
        ];
    }

    private function processTabs(array $data): array
    {
        $tabs = [];

        foreach ($data as $entry) {
            $dto = (new SheetTabDto())
                ->setId(Arr::get($entry, implode('.', [self::RESPONSE_PROPERTIES, self::RESPONSE_SHEET_ID])))
                ->setName(Arr::get($entry, implode('.', [self::RESPONSE_PROPERTIES, self::RESPONSE_TITLE])))
                ->setIndex(Arr::get($entry, implode('.', [self::RESPONSE_PROPERTIES, self::RESPONSE_INDEX])))
                ->setRowCount(
                    Arr::get(
                        $entry,
                        implode('.', [
                            self::RESPONSE_PROPERTIES,
                            self::RESPONSE_GRID_PROPERTIES,
                            self::RESPONSE_ROW_COUNT,
                        ]),
                    ),
                )
                ->setColumnCount(
                    Arr::get(
                        $entry,
                        implode('.', [
                            self::RESPONSE_PROPERTIES,
                            self::RESPONSE_GRID_PROPERTIES,
                            self::RESPONSE_COLUMN_COUNT,
                        ]),
                    ),
                );

            $tabs[] = $dto;
        }

        return $tabs;
    }
}
