<?php

namespace App\Domain\Google\Actions\Drive;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleFile;
use App\Domain\Google\Support\Enums\Drive\GoogleFileType;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class GoogleFileFromResponse
{
    public const RESPONSE_CAPABILITIES = 'capabilities';
    public const RESPONSE_NAME = 'name';
    public const RESPONSE_WEB_VIEW_LINK = 'webViewLink';
    public const RESPONSE_CREATED_TIME = 'createdTime';
    public const RESPONSE_MODIFIED_TIME = 'modifiedTime';
    public const RESPONSE_ID = 'id';
    public const RESPONSE_DRIVE_ID = 'driveId';

    public function execute(GoogleAccount $account, GoogleFileType $type, array $data): GoogleFile
    {
        return GoogleFile::query()->updateOrCreate(
            [
                GoogleFile::PROPERTY_EXTERNAL_ID => Arr::get($data, self::RESPONSE_ID),
                GoogleFile::PROPERTY_GOOGLE_ACCOUNT_ID => $account->getId(),
            ],
            [
                GoogleFile::PROPERTY_TYPE => $type,
                GoogleFile::PROPERTY_NAME => Arr::get($data, self::RESPONSE_NAME),
                GoogleFile::PROPERTY_URL => Arr::get($data, self::RESPONSE_WEB_VIEW_LINK),
                GoogleFile::PROPERTY_CAPABILITIES => Arr::get($data, self::RESPONSE_CAPABILITIES),
                GoogleFile::PROPERTY_EXTERNAL_CREATED_AT => Carbon::parse(Arr::get($data, self::RESPONSE_CREATED_TIME)),
                GoogleFile::PROPERTY_EXTERNAL_UPDATED_AT => Carbon::parse(
                    Arr::get($data, self::RESPONSE_MODIFIED_TIME),
                ),
            ],
        );
    }

    private function getDrive(string $externalId):
}
