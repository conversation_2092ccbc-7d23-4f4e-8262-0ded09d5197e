<?php

namespace App\Domain\Google\Actions\Drive;

use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleFile;
use App\Domain\Google\Support\Enums\Drive\GoogleFileType;
use App\Domain\Google\Support\Exceptions\GoogleRequestException;
use App\Domain\Google\Support\Requests\GoogleRequest;
use App\Support\Enums\RequestType;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class SyncGoogleFiles extends GoogleRequest
{
    public const ENDPOINT = 'https://www.googleapis.com/drive/v3/files';

    public const RESPONSE_FILES = 'files';

    public const RESPONSE_NEXT_PAGE_TOKEN = 'nextPageToken';

    private ?string $pageToken = null;
    private GoogleAccount $account;
    private GoogleFileType $fileType;

    public function execute(GoogleAccount $account, GoogleFileType $fileType, ?string $pageToken = null): ?string
    {
        $this->pageToken = $pageToken;
        $this->account = $account;
        $this->fileType = $fileType;

        $response = $this->getResponse(RequestType::GET, $account);

        dd($response);

        if (!$response->isSuccessful()) {
            throw GoogleRequestException::becauseOfHttpErrorWithStatusCode(
                $account,
                $response->getStatus(),
                $response->getBody(),
            );
        }

        $this->processRows(Arr::get($response->getBody(), self::RESPONSE_FILES, []));

        return Arr::get($response->getBody(), self::RESPONSE_NEXT_PAGE_TOKEN);
    }

    protected function getUrl(): string
    {
        return self::ENDPOINT;
    }

    protected function getQueryParameters(): ?array
    {
        return [
            'pageToken' => $this->pageToken,
            'q' => sprintf('mimeType="%s"', $this->fileType->mimeType()),
            'includeItemsFromAllDrives' => 'true',
            'supportsAllDrives' => 'true',
            'includeTeamDriveItems' => 'true',
            'fields' => sprintf(
                'files(%s),nextPageToken',
                implode(',', [
                    GoogleFileFromResponse::RESPONSE_CAPABILITIES,
                    GoogleFileFromResponse::RESPONSE_NAME,
                    GoogleFileFromResponse::RESPONSE_WEB_VIEW_LINK,
                    GoogleFileFromResponse::RESPONSE_CREATED_TIME,
                    GoogleFileFromResponse::RESPONSE_MODIFIED_TIME,
                    GoogleFileFromResponse::RESPONSE_ID,
                    GoogleFileFromResponse::RESPONSE_DRIVE_ID,
                ]),
            ),
            'corpora' => 'allDrives',
        ];
    }

    private function processRows(array $rows): void
    {
        foreach ($rows as $row) {
            app(GoogleFileFromResponse::class)->execute($this->account, $this->fileType, $row);
        }
    }
}
