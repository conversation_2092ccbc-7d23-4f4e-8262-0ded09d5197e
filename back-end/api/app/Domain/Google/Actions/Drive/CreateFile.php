<?php

namespace App\Domain\Google\Actions\Drive;

use App\Domain\Google\Dto\Drive\CreateFileDto;
use App\Domain\Google\Models\GoogleAccount;
use App\Domain\Google\Models\GoogleFile;
use App\Domain\Google\Support\Exceptions\GoogleRequestException;
use App\Domain\Google\Support\Requests\GoogleRequest;
use App\Support\Enums\RequestType;

class CreateFile extends GoogleRequest
{
    public const ENDPOINT = 'https://www.googleapis.com/drive/v3/files';

    private CreateFileDto $dto;

    public function execute(GoogleAccount $account, CreateFileDto $dto): GoogleFile
    {
        $this->dto = $dto;

        $response = $this->getResponse(RequestType::POST, $account);

        if (!$response->isSuccessful()) {
            throw GoogleRequestException::becauseOfHttpErrorWithStatusCode(
                $account,
                $response->getStatus(),
                $response->getBody(),
            );
        }

        return app(GoogleFileFromResponse::class)->execute($account, $dto->getType(), $response->getBody());
    }

    protected function getUrl(): string
    {
        return self::ENDPOINT;
    }

    protected function getQueryParameters(): ?array
    {
        return [
            'supportsAllDrives' => 'true',
            'fields' => implode(',', [
                GoogleFileFromResponse::RESPONSE_CAPABILITIES,
                GoogleFileFromResponse::RESPONSE_NAME,
                GoogleFileFromResponse::RESPONSE_WEB_VIEW_LINK,
                GoogleFileFromResponse::RESPONSE_CREATED_TIME,
                GoogleFileFromResponse::RESPONSE_MODIFIED_TIME,
                GoogleFileFromResponse::RESPONSE_ID,
                GoogleFileFromResponse::RESPONSE_DRIVE_ID,
            ]),
        ];
    }

    protected function getBody(): ?array
    {
        $data = [
            'name' => $this->dto->getName(),
            'mimeType' => $this->dto->getType()->mimeType(),
        ];

        if ($this->dto->getDrive()) {
            $data['parents'] = [$this->dto->getDrive()->getExternalId()];
        }

        return $data;
    }
}
